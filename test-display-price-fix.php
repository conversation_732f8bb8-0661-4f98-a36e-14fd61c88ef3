<?php
/**
 * Test script to verify the display price fixes for rejected bids
 * This tests both [allauctions] and [singleauction] shortcode price displays
 */

// Include WordPress
require_once('wp-config.php');

echo "<h2>Testing Display Price Fixes for Rejected Bids</h2>\n";

// Test lot ID - replace with actual lot ID that has rejected bids
$test_lot_id = 'LOT001'; // Change this to a real lot ID

echo "<h3>Testing Lot: $test_lot_id</h3>\n";

// Test the database functions
echo "<h4>Database Function Tests:</h4>\n";

// Test auction_has_bids
$has_bids = USF_Database::auction_has_bids($test_lot_id);
echo "auction_has_bids(): " . ($has_bids ? 'true' : 'false') . "\n";

// Test get_highest_bid (only pending/accepted)
$highest_bid_accepted = USF_Database::get_highest_bid($test_lot_id);
echo "get_highest_bid() (pending/accepted only): " . ($highest_bid_accepted ? '$' . number_format($highest_bid_accepted, 2) : 'null') . "\n";

// Test get_highest_bid_any_status (all bids)
$highest_bid_any = USF_Database::get_highest_bid_any_status($test_lot_id);
echo "get_highest_bid_any_status() (all bids): " . ($highest_bid_any ? '$' . number_format($highest_bid_any, 2) : 'null') . "\n";

// Get lot details
$lot = USF_Database::get_lot($test_lot_id);
if (!$lot) {
    echo "ERROR: Lot $test_lot_id not found!\n";
    exit;
}

echo "Starting price: $" . number_format($lot->min_offer, 2) . "\n";

// Test old vs new display price functions
$old_display_price = USF_Database::get_auction_display_price($test_lot_id, $lot->min_offer);
$new_display_price = USF_Database::get_auction_display_price_all_bids($test_lot_id, $lot->min_offer);

echo "\n<h4>Display Price Comparison:</h4>\n";
echo "OLD get_auction_display_price(): $" . number_format($old_display_price, 2) . "\n";
echo "NEW get_auction_display_price_all_bids(): $" . number_format($new_display_price, 2) . "\n";

// Determine what label should be shown
$price_label = $has_bids ? 'Updated Price' : 'Starting Price';
echo "Price label: $price_label\n";

echo "\n<h4>Expected Behavior:</h4>\n";
if ($has_bids) {
    echo "- Should show 'Updated Price' label\n";
    echo "- Should show highest bid amount regardless of status: $" . number_format($new_display_price, 2) . "\n";
} else {
    echo "- Should show 'Starting Price' label\n";
    echo "- Should show starting price: $" . number_format($lot->min_offer, 2) . "\n";
}

// Test all bids for this lot
echo "\n<h4>All Bids for this Lot:</h4>\n";
$all_bids = USF_Database::get_lot_bids($test_lot_id);
if (empty($all_bids)) {
    echo "No bids found for this lot.\n";
} else {
    foreach ($all_bids as $bid) {
        echo "- $" . number_format($bid->bid_amount, 2) . " (" . $bid->status . ") by " . $bid->user_email . "\n";
    }
}

echo "\n<h4>Test Results:</h4>\n";
if ($has_bids && $new_display_price > $old_display_price) {
    echo "✅ SUCCESS: New function shows higher price than old function\n";
    echo "✅ This means rejected bids are now being considered for display\n";
} elseif (!$has_bids) {
    echo "ℹ️  INFO: No bids exist, both functions should show starting price\n";
} elseif ($new_display_price == $old_display_price) {
    echo "ℹ️  INFO: Both functions show same price (no rejected bids or highest bid is accepted)\n";
} else {
    echo "❌ UNEXPECTED: New function shows lower price than old function\n";
}

echo "\n<h4>Shortcode Integration Test:</h4>\n";
echo "The following sections should now show the correct 'Updated Price':\n";
echo "- [allauctions] page: usf-detail-row price display\n";
echo "- [singleauction] page: usf-stat-value price display\n";
echo "- Both should show: $" . number_format($new_display_price, 2) . " with '$price_label' label\n";

?>
